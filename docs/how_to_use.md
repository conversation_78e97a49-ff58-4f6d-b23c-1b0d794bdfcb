# Betting Project - Comprehensive Documentation

## Overview
This is a comprehensive sports betting prediction system that scrapes data from multiple football leagues worldwide, processes the data, and generates predictions. The project has evolved significantly from its initial scope and now includes robust testing frameworks, URL management systems, and automated data processing pipelines.

## Project Status
- **314 leagues** supported across multiple countries and divisions
- **91%+ success rate** in data scraping (as of latest testing)
- **Automated testing framework** for continuous monitoring
- **Enhanced URL management** with granular control
- **Robust error handling** and progress tracking

## Core Workflow

### 1. Initial Setup & Configuration
The process begins with the master configuration file:
- **`src/config/leagues.json`** - Contains 314+ leagues with:
  - League name and country
  - Stats URL (SoccerStats.com)
  - Table URL (FootyStats.org)
  - Fixtures URL (FootyStats.org)

### 2. URL Extraction & Management
**Enhanced URL Extractor** (`src/url_extractor.py`):
```bash
# Extract URLs for all leagues
python3 src/url_extractor.py

# Extract URLs for specific league
python3 src/url_extractor.py --league "ENGLAND_PREMIER_LEAGUE"

# Extract only team URLs (skip H2H URLs)
python3 src/url_extractor.py --league "CHILE_PRIMERA_B" --team-urls-only --force

# Extract only H2H URLs (skip team URLs)
python3 src/url_extractor.py --league "SPAIN_LA_LIGA" --h2h-urls-only

# Force re-extraction (override progress tracking)
python3 src/url_extractor.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA" --force
```

**Features:**
- Selenium-based web scraping with rate limiting
- Progress tracking to avoid re-processing
- Granular control for partial updates
- Automatic retry mechanisms
- Chrome WebDriver integration

**Output:** Individual JSON config files in `src/config/json_database/`

### 3. Team Name Reconciliation
**Enhanced Team Name Mapper** (`team_name_mapper.py`):
```bash
# Map team names for all leagues
python3 team_name_mapper.py

# Map team names for specific league
python3 team_name_mapper.py --league "CHILE_PRIMERA_B"

# Map team names for multiple leagues
python3 team_name_mapper.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA"
```

**Purpose:**
- Fuzzy matching between SoccerStats and FootyStats team names
- Resolves naming inconsistencies between data sources
- Creates mapping dictionaries for accurate data correlation

### 4. Configuration Processing
**Step 4a: Overwrite Configuration** (`overwrite_config.py`):
```bash
# Process all leagues
python3 overwrite_config.py

# Process specific league
python3 overwrite_config.py --league "CHILE_PRIMERA_B"

# Process multiple leagues
python3 overwrite_config.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA"
```
- Converts JSON configs to Python dictionary format
- Outputs to `src/scrapers/generated_league_data.py`

**Step 4b: Split Configuration** (`src/scrapers/split_config.py`):
```bash
# Split all leagues
python3 src/scrapers/split_config.py

# Split specific league
python3 src/scrapers/split_config.py --league "CHILE_PRIMERA_B"
```
- Creates individual Python config files per league
- Outputs to `src/scrapers/league_configs/`
- Includes configuration variables for easy access

**Step 4c: Format Configuration Dictionaries** (`format_config_dictionaries.py`):
```bash
# Auto-format all league config files for better organization
python3 format_config_dictionaries.py
```
- Automatically formats URL dictionaries in all league config files
- Organizes URLs for better readability and maintenance
- Processes all 314+ config files in `src/scrapers/league_configs/`
- Essential for clean, organized configuration files

**Step 4d: Remove Match IDs** (`remove_match_ids.py`):
```bash
# Remove match IDs from H2H URLs to get general stats pages
python3 remove_match_ids.py
```
- Removes `#match_id` portions from HEAD_TO_HEAD_URLS
- Converts specific fixture URLs to general head-to-head stats pages
- Required before scraping to ensure proper H2H data collection
- Processes all league config files automatically

### 5. Data Scraping
**Prerequisites:** Complete steps 1-4 (including URL formatting and match ID removal) before scraping.

**Main Scraper** (`src/scrapers/main.py`):
```bash
# Scrape all data for a league
python src/scrapers/main.py --league "ENGLAND_PREMIER_LEAGUE"

# Refresh only results data
python src/scrapers/main.py --league "CHILE_PRIMERA_B" --refresh-results

# Scrape specific data types
python src/scrapers/main.py --league "SPAIN_LA_LIGA" --refresh-league-table --refresh-team-stats
```

**Data Types Scraped:**
- **League Table** - Current standings, points, goals
- **Results** - Historical match results with scores
- **Recent Results** - Latest match outcomes
- **Head-to-Head Stats** - Historical matchup data
- **League Stats** - Overall league statistics
- **Team Stats** - Individual team performance metrics

**Output:** CSV files organized by league in `data/raw/[LEAGUE_NAME]/`

### 6. Testing & Quality Assurance
**Comprehensive Testing Framework** (`efficient_testing_framework.py`):
```bash
# Test all leagues (smoke test)
python3 efficient_testing_framework.py --level smoke

# Test specific leagues
python3 efficient_testing_framework.py --level smoke --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA"

# Full integration test
python3 efficient_testing_framework.py --level integration --workers 4

# Test with detailed logging
python3 efficient_testing_framework.py --level smoke --workers 1 --leagues "CHILE_PRIMERA_B"
```

**Testing Levels:**
- **Smoke Test** - Basic data loading and validation
- **Integration Test** - Full scraping pipeline validation
- **Performance Test** - Speed and efficiency metrics

**Features:**
- Parallel processing with configurable workers
- Detailed failure tracking and reporting
- Progress monitoring with ETA calculations
- Automatic retry mechanisms

### 7. Advanced Prediction Engine
**Main Prediction System** (`src/main.py`):

The prediction engine is a sophisticated machine learning system with multiple input modes and comprehensive prediction capabilities.

**🎯 Input Modes:**

**Command Line Mode:**
```bash
# Single match prediction
python src/main.py --league ENGLAND_PREMIER_LEAGUE --home Liverpool --away Arsenal
```

**Interactive Mode:**
```bash
# Interactive team and league selection
python src/main.py --interactive
```

**Batch Mode:**
```bash
# Multiple predictions from YAML config
python src/main.py --config predictions.yaml
```

**Utility Commands:**
```bash
# List all available leagues
python src/main.py --list-leagues

# List teams in a specific league
python src/main.py --list-teams ENGLAND_PREMIER_LEAGUE
```

**🔮 Prediction Types:**
- **Three-Way** - Home Win / Draw / Away Win
- **Over/Under Goals** - 1.5, 2.5, 3.5 goal thresholds
- **Both Teams to Score (BTTS)** - Yes/No predictions
- **Double Chance** - Combined outcome predictions
- **Expected Goals (xG)** - Realistic goal expectation values
- **Correct Score Predictions** - Most likely exact scores

**🧠 Advanced Features:**
- **Confidence Analysis** - Statistical confidence levels for each prediction
- **Risk Assessment** - Risk evaluation for betting decisions
- **Feature Engineering** - Advanced statistical feature creation
- **Model Training** - Automated ML model training per league
- **Visualization Generation** - Prediction distribution charts and confidence heatmaps

**📊 Output Formats:**
- **Markdown Reports** - Detailed analysis with formatted tables
- **Excel Files** - Comprehensive spreadsheets with all predictions
- **Visualizations** - PNG charts and heatmaps
- **Detailed Logs** - League-specific logging with timestamps

**📁 Output Structure:**
```
data/processed/[LEAGUE_NAME]/
├── predictions/
│   ├── [match]_prediction_[timestamp].md
│   └── [match]_prediction_[timestamp].xlsx
├── images/
│   ├── prediction_distribution_three_way.png
│   ├── prediction_distribution_over_under_2_5.png
│   ├── prediction_confidence_heatmap.png
│   └── [additional visualizations]
└── logs/
    └── [match]_[timestamp].log
```

**Example Batch Configuration (predictions.yaml):**
```yaml
matches:
  - league: ENGLAND_PREMIER_LEAGUE
    fixtures:
      - home: Liverpool
        away: Arsenal
      - home: Manchester City
        away: Chelsea
  - league: SPAIN_LA_LIGA
    fixtures:
      - home: Barcelona
        away: Real Madrid
```

## Complete Workflow Summary

For a complete league setup and data processing, follow this exact sequence:

### **Quick Start - Single League Setup:**
```bash
# 1. Extract URLs for a specific league
python3 src/url_extractor.py --league "CHILE_PRIMERA_B" --force

# 2. Map team names
python3 team_name_mapper.py --league "CHILE_PRIMERA_B"

# 3. Update configuration
python3 overwrite_config.py --league "CHILE_PRIMERA_B"
python3 src/scrapers/split_config.py --league "CHILE_PRIMERA_B"

# 4. Format and clean URLs
python3 format_config_dictionaries.py
python3 remove_match_ids.py

# 5. Start scraping
python src/scrapers/main.py --league "CHILE_PRIMERA_B"

# 6. Generate predictions
python src/main.py --league "CHILE_PRIMERA_B" --home "Team A" --away "Team B"
```

### **Batch Processing - Multiple Leagues:**
```bash
# 1. Extract URLs for multiple leagues
python3 src/url_extractor.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A" --force

# 2. Map team names for all leagues
python3 team_name_mapper.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A"

# 3. Update configurations
python3 overwrite_config.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A"
python3 src/scrapers/split_config.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A"

# 4. Format and clean URLs (processes all leagues)
python3 format_config_dictionaries.py
python3 remove_match_ids.py

# 5. Test the updated leagues
python3 efficient_testing_framework.py --level smoke --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A"
```

### **Full System Maintenance:**
```bash
# 1. Extract URLs for all leagues (use with caution)
python3 src/url_extractor.py

# 2. Map team names for all leagues
python3 team_name_mapper.py

# 3. Update all configurations
python3 overwrite_config.py
python3 src/scrapers/split_config.py

# 4. Format and clean all URLs
python3 format_config_dictionaries.py
python3 remove_match_ids.py

# 5. Run comprehensive testing
python3 efficient_testing_framework.py --level smoke --workers 8
```

## Advanced Features

### URL Management & Maintenance
The project includes sophisticated URL management to handle website changes:

**Common Issues:**
- Website structure changes (e.g., `team.asp` → `teamstats.asp`)
- URL parameter modifications
- Team name variations between sources

**Solutions:**
- Automated URL validation and updating
- Granular re-extraction capabilities
- Progress tracking to avoid redundant work
- Comprehensive error logging

### Error Handling & Recovery
**Failed League Tracking** (`failed_leagues_tracker.md`):
- Automatic documentation of failed leagues
- Root cause analysis
- Recovery procedures
- Success rate monitoring

**Robust Scraping:**
- Rate limiting to avoid IP blocks
- User agent rotation
- Automatic retry with exponential backoff
- Graceful degradation on partial failures

## Project Directory Structure
```
betting_project/
│
├── data/                       # Data storage and processing
│   ├── raw/                    # Scraped CSV files organized by league (314+ directories)
│   │   ├── ENGLAND_PREMIER_LEAGUE/
│   │   │   ├── ENGLAND_PREMIER_LEAGUE_league_table.csv
│   │   │   ├── ENGLAND_PREMIER_LEAGUE_results.csv
│   │   │   ├── ENGLAND_PREMIER_LEAGUE_recent_results.csv
│   │   │   ├── ENGLAND_PREMIER_LEAGUE_h2h_stats.csv
│   │   │   ├── ENGLAND_PREMIER_LEAGUE_league_stats.csv
│   │   │   └── ENGLAND_PREMIER_LEAGUE_team_stats.csv
│   │   ├── SPAIN_LA_LIGA/
│   │   ├── CHILE_PRIMERA_B/
│   │   └── [314+ other league directories]
│   ├── processed/              # Prediction outputs and analysis
│   │   ├── ENGLAND_PREMIER_LEAGUE/
│   │   │   ├── predictions/    # Match prediction files (.md, .xlsx)
│   │   │   ├── images/         # Visualization outputs (.png)
│   │   │   └── logs/           # Prediction logs
│   │   └── img/                # Global analysis images
│   └── football_betting.db.backup # Database backup
│
├── src/                        # Core application modules
│   ├── main.py                 # 🎯 ADVANCED PREDICTION ENGINE
│   ├── url_extractor.py        # Enhanced URL extraction system
│   ├── utils.py                # Global utilities
│   ├── utils_imputation.py     # Data imputation utilities
│   │
│   ├── config/                 # Configuration management
│   │   ├── leagues.json        # Master league configuration (314+ leagues)
│   │   ├── json_database/      # Individual league JSON configs (314+ files)
│   │   │   ├── ENGLAND_PREMIER_LEAGUE_config.json
│   │   │   ├── SPAIN_LA_LIGA_config.json
│   │   │   └── [314+ other config files]
│   │   ├── progress/           # URL extraction progress tracking
│   │   └── sorted.py          # Utility to sort leagues.json
│   │
│   ├── scrapers/               # Data scraping system
│   │   ├── main.py            # Main scraping orchestrator
│   │   ├── results_scraper.py # Results data scraper
│   │   ├── league_table.py    # League table scraper
│   │   ├── team_stats.py      # Team statistics scraper
│   │   ├── head_to_head.py    # H2H statistics scraper
│   │   ├── league_stats.py    # League statistics scraper
│   │   ├── recent_results_scraper.py # Recent results scraper
│   │   ├── utils.py           # Scraping utilities
│   │   ├── split_config.py    # Configuration splitting utility
│   │   ├── generated_league_data.py # Generated from JSON configs
│   │   ├── league_configs/     # Individual Python config files (314+ files)
│   │   │   ├── ENGLAND_PREMIER_LEAGUE.py
│   │   │   ├── SPAIN_LA_LIGA.py
│   │   │   └── [314+ other config files]
│   │   ├── team_mapping_configs/ # Team name mapping configurations
│   │   ├── h2h_automation.py   # H2H scraping automation
│   │   ├── h2h_batch_scraper.py # Batch H2H processing
│   │   └── comment_urls.py     # URL commenting utilities
│   │
│   ├── data_loading/           # Data loading and validation
│   │   ├── core.py            # Core data loading functions
│   │   ├── database_loader.py # Database loading utilities
│   │   ├── csv_to_sqlite.py   # CSV to SQLite conversion
│   │   ├── validation.py      # Data validation
│   │   ├── constants.py       # Loading constants
│   │   └── utils.py           # Loading utilities
│   │
│   ├── feature_engineering/    # Feature creation and processing
│   │   ├── core.py            # Core feature engineering
│   │   ├── h2h.py             # Head-to-head features
│   │   ├── strength.py        # Team strength calculations
│   │   ├── constants.py       # Feature constants
│   │   └── utils.py           # Feature utilities
│   │
│   ├── model_training/         # Machine learning model training
│   │   ├── core.py            # Core training functions
│   │   ├── neural_network.py  # Neural network models
│   │   ├── cross_validation.py # Cross-validation
│   │   ├── feature_analysis.py # Feature importance analysis
│   │   ├── visualization.py   # Training visualizations
│   │   └── constants.py       # Training constants
│   │
│   ├── prediction/             # Prediction generation and analysis
│   │   ├── core.py            # Core prediction functions
│   │   ├── probabilities.py   # Probability calculations
│   │   ├── expected_goals.py  # xG calculations
│   │   ├── scores.py          # Score predictions
│   │   ├── analysis.py        # Prediction analysis
│   │   ├── excel_output.py    # Excel report generation
│   │   ├── markdown_output.py # Markdown report generation
│   │   ├── validation.py      # Prediction validation
│   │   └── constants.py       # Prediction constants
│   │
│   ├── analysis/               # Data analysis and visualization
│   │   ├── core.py            # Core analysis functions
│   │   ├── feature_importance.py # Feature importance analysis
│   │   ├── shap_analysis.py   # SHAP value analysis
│   │   ├── visualization.py   # Analysis visualizations
│   │   └── constants.py       # Analysis constants
│   │
│   └── database/               # Database management
│       ├── football_db.py     # Main database interface
│       ├── config.py          # Database configuration
│       └── utils.py           # Database utilities
│
├── checkpoints/                # H2H scraping progress tracking (314+ files)
│   ├── ENGLAND_PREMIER_LEAGUE_h2h_checkpoint.json
│   ├── ENGLAND_PREMIER_LEAGUE_h2h_progress.json
│   └── [314+ other checkpoint files]
│
├── analysis_output/            # Feature analysis visualizations
│   ├── feature_importance_*.png
│   ├── *_by_class.png
│   └── [various analysis charts]
│
├── models/                     # Trained ML models
│   ├── best_model_weights.weights.h5
│   ├── best_three_way_model.weights.h5
│   └── [other model files]
│
├── static/                     # Web application assets
│   ├── css/                   # Stylesheets
│   └── js/                    # JavaScript files
│
├── templates/                  # Web application templates
│   ├── base.html
│   ├── leagues.html
│   ├── team_detail.html
│   └── [other HTML templates]
│
├── memory-bank/                # AI assistant memory and context
│   ├── projectbrief.md
│   ├── progress.md
│   ├── techContext.md
│   └── [other context files]
│
├── roocode_tools_docs/         # Tool documentation
│   ├── TOOLS_INDEX.md
│   └── individual_tools/
│
├── Core Scripts & Utilities:
├── overwrite_config.py         # JSON to Python config converter
├── team_name_mapper.py         # Team name reconciliation system
├── efficient_testing_framework.py # Comprehensive testing system
├── web_app.py                  # Flask web application
├── create_database.py          # Database creation utility
├── betting_config.py           # Betting configuration
├── predictions.yaml            # Batch prediction configuration
│
├── Batch Processing & Automation:
├── batch_*.py                  # Various batch processing scripts
├── fix_*.py                    # League-specific fix scripts
├── run_*.py                    # Automation runner scripts
├── comprehensive_*.py          # Comprehensive processing scripts
│
├── Testing & Validation:
├── test_*.py                   # Various testing scripts
├── quick_*.py                  # Quick validation scripts
├── investigate_*.py            # Investigation utilities
├── verify_*.py                 # Verification scripts
│
├── Documentation & Tracking:
├── how_to_use.md              # 📖 This comprehensive documentation
├── failed_leagues_tracker.md  # Failed league tracking
├── LEAGUE_FIXING_GUIDE.md     # League fixing procedures
├── EFFICIENT_TESTING_STRATEGY.md # Testing strategy guide
├── DATABASE_MIGRATION_SUMMARY.md # Database migration info
├── BETTING_MARKETS_IMPLEMENTATION.md # Betting markets guide
├── scraping_features.md       # Scraping features documentation
│
├── Configuration & Setup:
├── requirements.txt           # Python dependencies
├── pyproject.toml            # Project configuration
├── uv.lock                   # Dependency lock file
├── betting_data.db           # Main SQLite database
│
└── Logs & Outputs:
    ├── *.log                  # Various log files
    ├── test_results_*.json    # Test result files
    ├── test_summary_*.md      # Test summary reports
    └── efficient_testing_*.log # Testing framework logs
```

## Troubleshooting & Maintenance

### Common Issues & Solutions

**1. URL Outdated Errors**
```
Problem: "Team 'X' not found in match: Y vs Z"
Solution: Re-extract team URLs for affected league
Command: python3 src/url_extractor.py --league "LEAGUE_NAME" --team-urls-only --force
```

**2. Website Structure Changes**
```
Problem: Scraping returns empty data or errors
Solution: Update URL patterns in url_extractor.py
Example: team.asp → teamstats.asp (already fixed)
```

**3. Rate Limiting Issues**
```
Problem: HTTP 429 errors or IP blocks
Solution: Adjust rate limiting in scrapers
Location: RateLimitedScraper class parameters
```

**4. Team Name Mismatches**
```
Problem: Team names don't match between sources
Solution: Re-run team name mapping
Command: python3 team_name_mapper.py --league "LEAGUE_NAME"
```

### Maintenance Procedures

**Weekly Health Check:**
```bash
# Test all leagues for basic functionality
python3 efficient_testing_framework.py --level smoke --workers 8

# Review failed leagues
cat failed_leagues_tracker.md

# Update URLs for failed leagues
python3 src/url_extractor.py --leagues "FAILED_LEAGUE_1,FAILED_LEAGUE_2" --force
```

**Monthly URL Refresh:**
```bash
# Refresh URLs for high-priority leagues
python3 src/url_extractor.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A" --force

# Update configurations
python3 overwrite_config.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A"
python3 src/scrapers/split_config.py --leagues "ENGLAND_PREMIER_LEAGUE,SPAIN_LA_LIGA,ITALY_SERIE_A"
```

## Development Utilities

### Bulk Operations with Neovim

**Autoformatting Multiple Python Files:**
```vim
# Load all Python files
:args **/*.py

# Autoformat all files
:argdo write

# Process with visual feedback
:argdo write | next
```

**Team Name Swapping in Config Files:**
```vim
# Swap team order in H2H URLs
:s/\v^(\s*#\s*")([^"]+) vs ([^"]+)(.*)$/\1\3 vs \2\4/

# Apply to all matching lines
:g/# "[^"]* vs [^"]*/s/\v^(\s*#\s*")([^"]+) vs ([^"]+)(.*)$/\1\3 vs \2\4/
```

### Performance Optimization

**Parallel Processing:**
- Use `--workers` parameter for testing framework
- Optimal worker count: 4-8 for most systems
- Monitor system resources during execution

**Memory Management:**
- Large leagues may require increased memory allocation
- Consider processing in batches for very large datasets
- Monitor memory usage during bulk operations

## Dependencies & Setup

**Required Python Packages:**
```
requests
beautifulsoup4
pandas
numpy
selenium
webdriver-manager
tenacity
fuzzywuzzy
python-levenshtein
rapidfuzz
tensorflow
scikit-learn
imblearn
openpyxl
python-dotenv
seaborn
shap
xgboost
matplotlib
argcomplete
```

**System Requirements:**
- Python 3.8+
- Google Chrome browser (for Selenium)
- 4GB+ RAM (8GB+ recommended for parallel processing)
- 10GB+ disk space for data storage

**Installation:**
```bash
# Install dependencies
uv pip install -r requirements.txt

# Verify Chrome installation
google-chrome --version

# Test basic functionality
python3 efficient_testing_framework.py --level smoke --leagues "ENGLAND_PREMIER_LEAGUE" --workers 1
```

## Project Evolution & Future Enhancements

**Recent Major Improvements:**
- Enhanced URL extraction with granular control
- Comprehensive testing framework with parallel processing
- Robust error handling and recovery mechanisms
- Automated progress tracking and reporting
- Command-line interfaces for all major components

**Planned Enhancements:**
- Machine learning model improvements
- Real-time data streaming capabilities
- Web dashboard for monitoring and control
- Automated league discovery and onboarding
- Enhanced prediction accuracy metrics

**Contributing:**
- Follow existing code patterns and naming conventions
- Add command-line arguments for new features
- Include comprehensive error handling
- Update documentation for any new functionality
- Test with multiple leagues before deployment

---

*This documentation reflects the current state of the betting project as of the latest major updates. The project has evolved significantly from its initial scope and continues to grow in capability and robustness.*
