import csv
import re
import os
from typing import Dict, Any, List, Union
import logging
from bs4 import BeautifulSoup, Tag

def get_data_directory(league_name: str, raw=True):
    # Navigate up two levels from the current file to reach the project root
    project_root = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
    data_dir = os.path.join(project_root, "data", "raw" if raw else "processed", league_name)
    os.makedirs(data_dir, exist_ok=True)
    return data_dir

def save_league_stats_to_csv(data: Dict[str, Any], league_name: str, filename: str):
    filepath = os.path.join(get_data_directory(league_name), filename)
    with open(filepath, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["Stat", "Value"])
        for stat, value in data.items():
            if stat != "table":  # Exclude the table data
                writer.writerow([stat, value])

def save_team_stats_to_csv(data: Dict[str, Dict[str, Any]], league_name: str, filename: str):
    filepath = os.path.join(get_data_directory(league_name), filename)

    # Collect all possible column names from all teams to ensure consistency
    all_columns = set()
    for team_stats in data.values():
        all_columns.update(team_stats.keys())

    # Define the expected column order to maintain compatibility with existing data
    expected_column_order = [
        # Basic match statistics
        "total_home_played", "total_home_wins", "total_home_draws", "total_home_losses",
        "total_away_played", "total_away_wins", "total_away_draws", "total_away_losses",
        "total_played", "total_wins", "total_draws", "total_losses",

        # Points per game
        "points_per_game", "home_points_per_game", "away_points_per_game",

        # Goals scored
        "goals_scored_home", "goals_scored_away", "goals_scored_all",
        "goals_scored_per_match_home", "goals_scored_per_match_away", "goals_scored_per_match_all",

        # Goals conceded
        "goals_conceded_home", "goals_conceded_away", "goals_conceded_all",
        "goals_conceded_per_match_home", "goals_conceded_per_match_away", "goals_conceded_per_match_all",

        # Goals for + against per match
        "gf_ga_per_match_home", "gf_ga_per_match_away", "gf_ga_per_match_all",

        # Over/under percentages (full time)
        "gf_ga_over_0_5_percentage_home", "gf_ga_over_0_5_percentage_away", "gf_ga_over_0_5_percentage_all",
        "gf_ga_over_1_5_percentage_home", "gf_ga_over_1_5_percentage_away", "gf_ga_over_1_5_percentage_all",
        "gf_ga_over_2_5_percentage_home", "gf_ga_over_2_5_percentage_away", "gf_ga_over_2_5_percentage_all",
        "gf_ga_over_3_5_percentage_home", "gf_ga_over_3_5_percentage_away", "gf_ga_over_3_5_percentage_all",
        "gf_ga_over_4_5_percentage_home", "gf_ga_over_4_5_percentage_away", "gf_ga_over_4_5_percentage_all",
        "gf_ga_over_5_5_percentage_home", "gf_ga_over_5_5_percentage_away", "gf_ga_over_5_5_percentage_all",

        # Over/under percentages (half time)
        "gf_ga_over_0_5_ht_percentage_home", "gf_ga_over_0_5_ht_percentage_away", "gf_ga_over_0_5_ht_percentage_all",
        "gf_ga_over_1_5_ht_percentage_home", "gf_ga_over_1_5_ht_percentage_away", "gf_ga_over_1_5_ht_percentage_all",
        "gf_ga_over_2_5_ht_percentage_home", "gf_ga_over_2_5_ht_percentage_away", "gf_ga_over_2_5_ht_percentage_all",

        # Last 8 games statistics (should be at the end)
        "ppg_last_8", "avg_goals_scored_last_8", "avg_goals_conceded_last_8"
    ]

    # Order columns: first use expected order, then add any remaining columns alphabetically
    ordered_columns = []
    remaining_columns = set(all_columns)

    # Add columns in expected order if they exist
    for col in expected_column_order:
        if col in all_columns:
            ordered_columns.append(col)
            remaining_columns.remove(col)

    # Add any remaining columns alphabetically (for new columns not in expected order)
    ordered_columns.extend(sorted(remaining_columns))

    headers = ["Team"] + ordered_columns

    with open(filepath, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(headers)

        for team, stats in data.items():
            # Ensure each team has a value for every column (use None for missing)
            row = [team] + [stats.get(col, None) for col in ordered_columns]
            writer.writerow(row)

def save_results_to_csv(results: List[List[str]], league_name: str, filename: str, append: bool = False):
    filepath = os.path.join(get_data_directory(league_name), filename)
    file_exists = os.path.exists(filepath)
    mode = "a" if append else "w"
    
    with open(filepath, mode, newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        if not append or not file_exists or os.path.getsize(filepath) == 0:
            writer.writerow(["Date", "Home Team", "Score", "Away Team", "Result", "Team"])
        
        # Only write rows if there are results to write, especially important for append mode
        if results:
            writer.writerows(results)

def save_head_to_head_stats_to_csv(data: List[Dict[str, Any]], league_name: str, filename: str, append: bool = False):
    filepath = os.path.join(get_data_directory(league_name), filename)
    if not data:
        logging.warning(f"No data provided for H2H stats in {filename}. Skipping save.")
        return

    file_exists = os.path.exists(filepath)
    
    # If appending, check for duplicates
    existing_matchups = set()
    if append and file_exists:
        try:
            import pandas as pd
            existing_df = pd.read_csv(filepath)
            existing_matchups = set(existing_df['Matchup'].tolist())
        except Exception as e:
            logging.warning(f"Could not read existing CSV for duplicate check: {e}")
    
    # Filter out duplicates from new data
    if existing_matchups:
        original_count = len(data)
        data = [row for row in data if row.get('Matchup') not in existing_matchups]
        if len(data) < original_count:
            logging.info(f"Filtered out {original_count - len(data)} duplicate matchups")
    
    # If no new data after filtering, skip saving
    if not data:
        logging.info("No new data to save after duplicate filtering")
        return
    
    mode = "a" if append else "w"
    
    with open(filepath, mode, newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        headers = list(data[0].keys()) # Determine headers from the first data item

        if not append or not file_exists or os.path.getsize(filepath) == 0:
            writer.writerow(headers)
        
        for row_dict in data:
            # Ensure values are written in the same order as headers
            # Use row_dict.get(header) to handle cases where a new row might miss a rare, old header
            # However, for H2H, it's more likely new data will conform or add new columns.
            # If new columns are added during an append, they won't have a header if appending to an old file.
            # This simple append assumes new data has a subset or exact match of existing headers if appending.
            # A more robust solution would involve reading existing headers and aligning data or re-writing the file.
            writer.writerow([row_dict.get(header) for header in headers])


def save_recent_results_to_csv(results: List[List[str]], league_name: str, filename: str, append: bool = False):
    filepath = os.path.join(get_data_directory(league_name), filename)
    file_exists = os.path.exists(filepath)
    mode = "a" if append else "w"

    with open(filepath, mode, newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        if not append or not file_exists or os.path.getsize(filepath) == 0:
            writer.writerow(["Date", "Home Team", "Home Score", "Away Team", "Away Score"])
        
        if results: # Only write if there are results
            writer.writerows(results)

def save_league_table_to_csv(league_table: list, league_name: str, filename: str):
    filepath = os.path.join(get_data_directory(league_name), filename)
    with open(filepath, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["Position", "Team", "MP", "W", "D", "L", "GF", "GA", "GD", "Pts"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for row in league_table:
            writer.writerow(row)

from typing import Any, Union # Ensure Any and Union are imported if not already
from bs4 import BeautifulSoup, Tag # Ensure Tag is imported

def safe_extract(
    soup_or_element: Union[BeautifulSoup, Tag, Any], # More specific type hint, fallback to Any
    selector: str = None,
    attr: str = None,
    value_type: type = str,
    use_direct_element: bool = False
) -> Any:
    try:
        element = None  # Initialize element
        if use_direct_element:
            # Basic check if soup_or_element is likely a Tag or BeautifulSoup object
            if not hasattr(soup_or_element, 'text') or not callable(getattr(soup_or_element, 'get', None)):
                logging.warning(
                    f"use_direct_element is True, but soup_or_element (type: {type(soup_or_element)}) "
                    "does not appear to be a valid BeautifulSoup Tag or element."
                )
                return None
            element = soup_or_element
        elif selector:  # If not use_direct_element, selector is mandatory
            # Basic check if soup_or_element is likely a BeautifulSoup object or a Tag that can search
            if not hasattr(soup_or_element, 'select_one') or not callable(soup_or_element.select_one):
                logging.warning(
                    f"use_direct_element is False, but soup_or_element (type: {type(soup_or_element)}) "
                    "does not have a 'select_one' method."
                )
                return None
            element = soup_or_element.select_one(selector)
        else:
            # This case: use_direct_element is False AND selector is None (or empty)
            logging.warning(
                "safe_extract: 'selector' must be provided if 'use_direct_element' is False."
            )
            return None
        if element:
            value = element.get(attr) if attr else element.text.strip()
            logging.debug(f"Extracted value for selector '{selector}': {value}")
            logging.debug(f"Type of extracted value: {type(value)}")
            if value_type == int:
                match = re.search(r"\d+", value)
                if match:
                    logging.debug(f"Converting '{value}' to int: {int(match.group())}")
                    return int(match.group())
                else:
                    logging.warning(f"No integer found in value '{value}' for selector '{selector}'")
                    return None
            elif value_type == float:
                match = re.search(r"\d+\.?\d*", value)
                if match:
                    logging.debug(f"Converting '{value}' to float: {float(match.group())}")
                    return float(match.group())
                else:
                    logging.warning(f"No float found in value '{value}' for selector '{selector}'")
                    return None
            else:
                logging.debug(f"Returning value '{value}' for selector '{selector}'")
                return value
    except Exception as e:
        logging.warning(f"Error extracting {selector}: {str(e)}")
    return None
