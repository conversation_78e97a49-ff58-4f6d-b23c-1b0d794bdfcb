"""
Utility functions for feature engineering.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Optional, Union, List, Tuple
from .constants import MIN_MATCHES_VALID, STRING_COLUMNS

logger = logging.getLogger(__name__)

def validate_team_stats(
    home_team: str,
    away_team: str,
    team_stats: pd.DataFrame
) -> <PERSON><PERSON>[Optional[pd.Series], Optional[pd.Series]]:
    """
    Validate and retrieve team statistics with case-insensitive fallback.

    Args:
        home_team: Name of home team
        away_team: Name of away team
        team_stats: DataFrame containing team statistics

    Returns:
        Tuple of (home_stats, away_stats) or (None, None) if validation fails
    """
    try:
        # First attempt: Exact string matching (original behavior)
        home_team_stats = team_stats[team_stats["Team"] == home_team]
        away_team_stats = team_stats[team_stats["Team"] == away_team]

        if not home_team_stats.empty and not away_team_stats.empty:
            return home_team_stats.iloc[0], away_team_stats.iloc[0]

        # Second attempt: Case-insensitive matching
        home_team_stats_ci = team_stats[team_stats["Team"].str.lower() == home_team.lower()]
        away_team_stats_ci = team_stats[team_stats["Team"].str.lower() == away_team.lower()]

        if not home_team_stats_ci.empty and not away_team_stats_ci.empty:
            logger.info(f"Found teams using case-insensitive matching: {home_team} vs {away_team}")
            return home_team_stats_ci.iloc[0], away_team_stats_ci.iloc[0]

        # Third attempt: Fuzzy matching for common variations
        home_team_fuzzy = _find_fuzzy_team_match(home_team, team_stats)
        away_team_fuzzy = _find_fuzzy_team_match(away_team, team_stats)

        if home_team_fuzzy is not None and away_team_fuzzy is not None:
            home_team_stats_fuzzy = team_stats[team_stats["Team"] == home_team_fuzzy]
            away_team_stats_fuzzy = team_stats[team_stats["Team"] == away_team_fuzzy]

            if not home_team_stats_fuzzy.empty and not away_team_stats_fuzzy.empty:
                logger.info(f"Found teams using fuzzy matching: {home_team}→{home_team_fuzzy}, {away_team}→{away_team_fuzzy}")
                return home_team_stats_fuzzy.iloc[0], away_team_stats_fuzzy.iloc[0]

        # If all attempts fail, log warning
        logger.warning(f"Missing team stats for match: {home_team} vs {away_team}")
        return None, None

    except Exception as e:
        logger.error(f"Error validating team stats: {str(e)}")
        return None, None

def check_form_data_validity(
    home_stats: pd.Series,
    away_stats: pd.Series,
    column_mapping: Dict[str, str]
) -> Tuple[str, int]:
    """
    Check if teams have played enough matches for valid form data.

    Args:
        home_stats: Statistics for home team
        away_stats: Statistics for away team
        column_mapping: Dictionary mapping feature names to column names

    Returns:
        Tuple of (validity string, validity flag)
    """
    try:
        # Handle case where column_mapping is empty or missing keys
        # Use direct column names as fallback
        home_played_col = column_mapping.get("total_home_played", "total_home_played")
        away_played_col = column_mapping.get("total_away_played", "total_away_played")

        # Check if columns exist in the stats
        if home_played_col not in home_stats.index or away_played_col not in home_stats.index:
            logger.warning(f"Missing columns for form validation: {home_played_col}, {away_played_col}")
            return "Cannot validate (missing data)", 0

        home_matches_played = (
            home_stats[home_played_col] +
            home_stats[away_played_col]
        )
        away_matches_played = (
            away_stats[home_played_col] +
            away_stats[away_played_col]
        )

        is_valid = min(home_matches_played, away_matches_played) >= MIN_MATCHES_VALID
        validity_str = (
            "Valid (5+ matches played)"
            if is_valid
            else "Not valid (less than 5 matches played)"
        )

        return validity_str, int(is_valid)

    except Exception as e:
        # Only log this error once per run to avoid spam
        if not hasattr(check_form_data_validity, '_error_logged'):
            logger.error(f"Error checking form data validity: {str(e)}")
            check_form_data_validity._error_logged = True
        return "Error checking validity", 0

def get_match_outcome_features(
    home_score: int,
    away_score: int
) -> Dict[str, str]:
    """
    Generate match outcome features from scores.

    Args:
        home_score: Number of goals scored by home team
        away_score: Number of goals scored by away team

    Returns:
        Dictionary of outcome features
    """
    try:
        total_goals = home_score + away_score
        
        # Determine match result
        if home_score > away_score:
            result = 0  # Home win
            three_way = "Home"
        elif home_score < away_score:
            result = 2  # Away win
            three_way = "Away"
        else:
            result = 1  # Draw
            three_way = "Draw"

        # Note: double_chance is now calculated from three_way probabilities, not stored as feature

        return {
            "result": result,
            "three_way": three_way,
            "home_goals": home_score,
            "away_goals": away_score,
            "total_goals": total_goals,
            "btts": "Yes" if home_score > 0 and away_score > 0 else "No",
            "over_under_1_5": "Over 1.5" if total_goals > 1.5 else "Under 1.5",
            "over_under_2_5": "Over 2.5" if total_goals > 2.5 else "Under 2.5",
            "over_under_3_5": "Over 3.5" if total_goals > 3.5 else "Under 3.5",
        }

    except Exception as e:
        logger.error(f"Error generating match outcome features: {str(e)}")
        return {}

def normalize_features(features: Dict[str, Union[str, float]]) -> Dict[str, Union[str, float]]:
    """
    Normalize numerical features to appropriate ranges.

    Args:
        features: Dictionary of features

    Returns:
        Dictionary of normalized features
    """
    try:
        # Create a copy to avoid modifying the original
        normalized = features.copy()

        # Normalize points per game (max is 3)
        for key in ["home_points_per_game", "away_points_per_game"]:
            if key in normalized:
                normalized[key] = normalized[key] / 3.0

        # Normalize goal ratios
        for key in [
            "home_goals_scored_ratio",
            "away_goals_scored_ratio",
            "home_goals_conceded_ratio",
            "away_goals_conceded_ratio"
        ]:
            if key in normalized:
                normalized[key] = np.clip(normalized[key], 0, 3) / 3.0

        # Normalize win rates and form
        for key in [
            "home_win_rate",
            "away_win_rate",
            "home_form",
            "away_form",
            "h2h_home_recent_win_rate",
            "h2h_away_recent_win_rate",
            "h2h_recent_draw_rate"
        ]:
            if key in normalized:
                normalized[key] = np.clip(normalized[key], 0, 1)

        # Normalize momentum features
        for key in ["home_form_momentum", "away_form_momentum"]:
            if key in normalized:
                normalized[key] = np.clip(normalized[key], -1, 1)

        return normalized

    except Exception as e:
        logger.error(f"Error normalizing features: {str(e)}")
        return features

def get_numeric_columns(df: pd.DataFrame) -> List[str]:
    """
    Get list of numeric columns, excluding string columns.

    Args:
        df: DataFrame to analyze

    Returns:
        List of numeric column names
    """
    return [col for col in df.columns if col not in STRING_COLUMNS]

def _find_fuzzy_team_match(target_team: str, team_stats: pd.DataFrame) -> Optional[str]:
    """
    Find fuzzy match for team name to handle common variations.

    Args:
        target_team: Team name to find match for
        team_stats: DataFrame containing team statistics

    Returns:
        Matched team name or None if no match found
    """
    if team_stats.empty or "Team" not in team_stats.columns:
        return None

    available_teams = team_stats["Team"].tolist()
    target_normalized = _normalize_team_name(target_team)

    # Check for normalized matches
    for team in available_teams:
        if _normalize_team_name(team) == target_normalized:
            return team

    # Check for partial matches (one name contains the other)
    target_lower = target_team.lower()
    for team in available_teams:
        team_lower = team.lower()
        if (target_lower in team_lower and len(target_lower) > 3) or \
           (team_lower in target_lower and len(team_lower) > 3):
            return team

    return None

def _normalize_team_name(team_name: str) -> str:
    """
    Normalize team name for comparison by removing common variations.

    Args:
        team_name: Original team name

    Returns:
        Normalized team name
    """
    import re

    if not team_name:
        return ""

    # Convert to lowercase and strip whitespace
    normalized = team_name.lower().strip()

    # Remove common prefixes
    prefixes = ['fc ', 'ac ', 'sc ', 'cf ', 'cd ', 'nk ', 'fk ', 'ks ', 'kf ']
    for prefix in prefixes:
        if normalized.startswith(prefix):
            normalized = normalized[len(prefix):].strip()
            break

    # Remove common suffixes
    suffixes = [' fc', ' ac', ' sc', ' cf', ' cd']
    for suffix in suffixes:
        if normalized.endswith(suffix):
            normalized = normalized[:-len(suffix)].strip()
            break

    # Remove special characters and normalize spacing
    normalized = re.sub(r'[^\w\s]', '', normalized)
    normalized = re.sub(r'\s+', ' ', normalized.strip())

    return normalized
