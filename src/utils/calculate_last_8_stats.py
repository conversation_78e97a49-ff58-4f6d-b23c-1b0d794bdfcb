#!/usr/bin/env python3
"""
Script to calculate and populate missing "last 8 games" statistics in team_stats.csv files.

This script calculates:
- ppg_last_8: Points per game in last 8 matches
- avg_goals_scored_last_8: Average goals scored in last 8 matches  
- avg_goals_conceded_last_8: Average goals conceded in last 8 matches
- total_points: Total points earned by the team

The calculations are based on the results.csv file for each league.
"""

import os
import sys
import pandas as pd
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Add the src directory to the path so we can import modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from data_loader import get_available_leagues
except ImportError:
    # Fallback: manually get available leagues
    def get_available_leagues():
        data_dir = 'data/raw'
        if os.path.exists(data_dir):
            return [d for d in os.listdir(data_dir)
                   if os.path.isdir(os.path.join(data_dir, d)) and not d.startswith('.')]
        return []


def parse_score(score_str: str) -> Tuple[int, int]:
    """Parse score string like '2 - 1' into tuple (2, 1)."""
    try:
        parts = score_str.split(' - ')
        if len(parts) == 2:
            return int(parts[0]), int(parts[1])
    except (ValueError, IndexError):
        pass
    return 0, 0


def calculate_points(result: int) -> int:
    """Calculate points based on result code."""
    if result == 0:  # Win
        return 3
    elif result == 1:  # Draw
        return 1
    elif result in [2, 3, 4, 5]:  # Loss (various codes)
        return 0
    else:
        return 0


def parse_date(date_str: str) -> datetime:
    """Parse date string into datetime object."""
    try:
        # Handle different date formats
        if len(date_str.split()) == 2:  # "5 May" format
            day, month = date_str.split()
            # Assume 2025 for recent matches (current season)
            year = 2025
            return datetime.strptime(f"{day} {month} {year}", "%d %b %Y")
        else:
            # Try other common formats
            for fmt in ["%d %b %Y", "%Y-%m-%d", "%d/%m/%Y"]:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
    except Exception:
        pass

    # Return a default date if parsing fails
    return datetime(2024, 1, 1)


def calculate_team_last_8_stats(results_df: pd.DataFrame, team_name: str, debug: bool = False) -> Dict[str, float]:
    """Calculate last 8 games statistics for a specific team."""

    # Filter results for this team
    team_results = results_df[results_df['Team'] == team_name].copy()

    if team_results.empty:
        return {
            'ppg_last_8': 0.0,
            'avg_goals_scored_last_8': 0.0,
            'avg_goals_conceded_last_8': 0.0,
            'total_points': 0
        }

    # Parse dates and sort by date (most recent first)
    team_results['parsed_date'] = team_results['Date'].apply(parse_date)
    team_results = team_results.sort_values('parsed_date', ascending=False)

    # Calculate total points for all matches
    team_results['points'] = team_results['Result'].apply(calculate_points)
    total_points = team_results['points'].sum()

    # Get last 8 matches
    last_8_matches = team_results.head(8)

    if debug and team_name == "Beijing Guoan":
        print(f"\n🔍 DEBUG for {team_name}:")
        print(f"   Total matches: {len(team_results)}")
        print(f"   Last 8 matches:")
        for i, (_, match) in enumerate(last_8_matches.iterrows()):
            home_team = match['Home Team']
            away_team = match['Away Team']
            score = match['Score']
            result = match['Result']
            points = calculate_points(result)
            print(f"     {i+1}. {match['Date']}: {home_team} {score} {away_team} (Result: {result}, Points: {points})")

    if len(last_8_matches) == 0:
        return {
            'ppg_last_8': 0.0,
            'avg_goals_scored_last_8': 0.0,
            'avg_goals_conceded_last_8': 0.0,
            'total_points': total_points
        }

    # Calculate points per game for last 8
    points_last_8 = last_8_matches['points'].sum()
    ppg_last_8 = points_last_8 / len(last_8_matches)

    # Calculate goals scored and conceded for last 8
    goals_scored_last_8 = []
    goals_conceded_last_8 = []

    for _, match in last_8_matches.iterrows():
        home_team = match['Home Team']
        away_team = match['Away Team']
        score = match['Score']

        home_goals, away_goals = parse_score(score)

        if team_name == home_team:
            # Team played at home
            goals_scored_last_8.append(home_goals)
            goals_conceded_last_8.append(away_goals)
        else:
            # Team played away
            goals_scored_last_8.append(away_goals)
            goals_conceded_last_8.append(home_goals)

    avg_goals_scored_last_8 = sum(goals_scored_last_8) / len(goals_scored_last_8) if goals_scored_last_8 else 0.0
    avg_goals_conceded_last_8 = sum(goals_conceded_last_8) / len(goals_conceded_last_8) if goals_conceded_last_8 else 0.0

    if debug and team_name == "Beijing Guoan":
        print(f"   Goals scored: {goals_scored_last_8} = {avg_goals_scored_last_8:.3f} avg")
        print(f"   Goals conceded: {goals_conceded_last_8} = {avg_goals_conceded_last_8:.3f} avg")
        print(f"   Points: {points_last_8}/{len(last_8_matches)} = {ppg_last_8:.3f} PPG")

    return {
        'ppg_last_8': ppg_last_8,
        'avg_goals_scored_last_8': avg_goals_scored_last_8,
        'avg_goals_conceded_last_8': avg_goals_conceded_last_8,
        'total_points': total_points
    }


def update_team_stats_file(league_name: str, dry_run: bool = False) -> bool:
    """Update team stats file with calculated last 8 statistics."""
    
    # File paths
    data_dir = os.path.join('data', 'raw', league_name)
    team_stats_file = os.path.join(data_dir, f'{league_name}_team_stats.csv')
    results_file = os.path.join(data_dir, f'{league_name}_results.csv')
    
    # Check if files exist
    if not os.path.exists(team_stats_file):
        print(f"❌ Team stats file not found: {team_stats_file}")
        return False
    
    if not os.path.exists(results_file):
        print(f"❌ Results file not found: {results_file}")
        return False
    
    try:
        # Load data
        print(f"📊 Loading data for {league_name}...")
        team_stats_df = pd.read_csv(team_stats_file)
        results_df = pd.read_csv(results_file)
        
        print(f"   Teams in stats: {len(team_stats_df)}")
        print(f"   Total results: {len(results_df)}")
        
        # Check if columns exist
        required_columns = ['ppg_last_8', 'avg_goals_scored_last_8', 'avg_goals_conceded_last_8']
        missing_columns = [col for col in required_columns if col not in team_stats_df.columns]
        
        if missing_columns:
            print(f"❌ Missing columns in team stats: {missing_columns}")
            return False
        
        # Calculate statistics for each team
        updated_teams = 0
        for idx, row in team_stats_df.iterrows():
            team_name = row['Team']
            
            # Calculate last 8 stats (enable debug for Beijing Guoan)
            debug_mode = team_name == "Beijing Guoan"
            stats = calculate_team_last_8_stats(results_df, team_name, debug=debug_mode)
            
            # Update the dataframe
            team_stats_df.at[idx, 'ppg_last_8'] = stats['ppg_last_8']
            team_stats_df.at[idx, 'avg_goals_scored_last_8'] = stats['avg_goals_scored_last_8']
            team_stats_df.at[idx, 'avg_goals_conceded_last_8'] = stats['avg_goals_conceded_last_8']
            
            # Add total_points column if it doesn't exist
            if 'total_points' not in team_stats_df.columns:
                team_stats_df['total_points'] = 0
            team_stats_df.at[idx, 'total_points'] = stats['total_points']
            
            updated_teams += 1
            
            print(f"   ✅ {team_name}: PPG={stats['ppg_last_8']:.2f}, "
                  f"Goals={stats['avg_goals_scored_last_8']:.2f}, "
                  f"Conceded={stats['avg_goals_conceded_last_8']:.2f}, "
                  f"Points={stats['total_points']}")
        
        if dry_run:
            print(f"\n🔍 DRY RUN: Would update {updated_teams} teams in {league_name}")
            return True
        else:
            # Save the updated file
            team_stats_df.to_csv(team_stats_file, index=False)
            print(f"\n💾 Updated {updated_teams} teams in {team_stats_file}")
            return True
            
    except Exception as e:
        print(f"❌ Error processing {league_name}: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Calculate and populate last 8 games statistics')
    parser.add_argument('--league', type=str, help='Specific league to process')
    parser.add_argument('--all', action='store_true', help='Process all available leagues')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be updated without making changes')
    
    args = parser.parse_args()
    
    if not args.league and not args.all:
        print("❌ Please specify either --league LEAGUE_NAME or --all")
        return
    
    print("🏆 LAST 8 GAMES STATISTICS CALCULATOR")
    print("=" * 50)
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No files will be modified")
        print("=" * 50)
    
    if args.league:
        # Process single league
        leagues = [args.league]
    else:
        # Process all leagues
        leagues = get_available_leagues()
        print(f"📋 Found {len(leagues)} leagues to process")
    
    successful = 0
    failed = 0
    
    for league in leagues:
        print(f"\n🔄 Processing {league}...")
        if update_team_stats_file(league, dry_run=args.dry_run):
            successful += 1
        else:
            failed += 1
    
    print(f"\n📊 SUMMARY:")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    
    if args.dry_run:
        print(f"\n💡 Run without --dry-run to apply changes")


if __name__ == '__main__':
    main()
